{
    // Workspace Settings Snippet - Plejeplan Project Configuration
    // Optimized to work with global settings and project-specific needs

    // Editor rulers aligned with global settings (80 for code, 120 for text)
    "editor.rulers": [80, 120],

    // Project-specific Python environment (inherits modern Ruff+Black setup from global)
    "python.envFile": "${workspaceFolder}/.env",
    "python.defaultInterpreterPath": "${workspaceFolder}/.venv/Scripts/python.exe",

    // Quarto/Markdown configuration - disable linting for markdown, enable for Python in .qmd
    "[markdown]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.wordWrap": "bounded",
        "editor.wordWrapColumn": 120,
        "editor.rulers": [80, 120],
        // Disable markdown linting to only lint Python code in .qmd files
        "editor.codeActionsOnSave": {},
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    },

    // Quarto-specific settings for .qmd files
    "[quarto]": {
        "editor.defaultFormatter": "quarto.quarto",
        "editor.wordWrap": "bounded",
        "editor.wordWrapColumn": 120,
        "editor.rulers": [80, 120],
        // Python code in .qmd should use 80 chars, text can use 120
        "python.analysis.typeCheckingMode": "basic"
    },

    // Project-specific spell checker words
    "cSpell.words": ["Gudenåengene", "Hornbæk", "plejeplan", "matr"],

    // Project-specific file associations
    "files.associations": {
        "*.qmd": "quarto"
    },

    // Project-specific search exclusions (minimal, inherit most from global)
    "search.exclude": {
        "**/.qmd_cache": true,
        "**/~$*.txt": true,
        "**/_freeze": true
    },

    // Quarto rendering settings for this project
    "quarto.render.renderOnSave": false,
    "quarto.render.previewType": "external"
}
