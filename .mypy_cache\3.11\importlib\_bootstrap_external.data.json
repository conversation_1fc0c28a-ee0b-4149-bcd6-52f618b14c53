{".class": "MypyFile", "_fullname": "importlib._bootstrap_external", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.BYTECODE_SUFFIXES", "kind": "Gdef"}, "DEBUG_BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.DEBUG_BYTECODE_SUFFIXES", "kind": "Gdef"}, "EXTENSION_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.EXTENSION_SUFFIXES", "kind": "Gdef"}, "ExtensionFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.ExtensionFileLoader", "kind": "Gdef"}, "FileFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.FileFinder", "kind": "Gdef"}, "FileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.FileLoader", "kind": "Gdef"}, "MAGIC_NUMBER": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.MAGIC_NUMBER", "kind": "Gdef"}, "NamespaceLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.NamespaceLoader", "kind": "Gdef"}, "OPTIMIZED_BYTECODE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.OPTIMIZED_BYTECODE_SUFFIXES", "kind": "Gdef"}, "PathFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.PathFinder", "kind": "Gdef"}, "SOURCE_SUFFIXES": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SOURCE_SUFFIXES", "kind": "Gdef"}, "SourceFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SourceFileLoader", "kind": "Gdef"}, "SourceLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SourceLoader", "kind": "Gdef"}, "SourcelessFileLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.SourcelessFileLoader", "kind": "Gdef"}, "WindowsRegistryFinder": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.WindowsRegistryFinder", "kind": "Gdef"}, "_NamespaceLoader": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external._NamespaceLoader", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib._bootstrap_external.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "cache_from_source": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.cache_from_source", "kind": "Gdef"}, "decode_source": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.decode_source", "kind": "Gdef"}, "path_sep": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.path_sep", "kind": "Gdef"}, "path_sep_tuple": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.path_sep_tuple", "kind": "Gdef"}, "path_separators": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.path_separators", "kind": "Gdef"}, "source_from_cache": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.source_from_cache", "kind": "Gdef"}, "spec_from_file_location": {".class": "SymbolTableNode", "cross_ref": "_frozen_importlib_external.spec_from_file_location", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\importlib\\_bootstrap_external.pyi"}